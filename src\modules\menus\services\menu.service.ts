import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Menu } from '../entities/menu.entity';
import { MenuDto, MenuTreeDto } from '../dto/menu.dto';

@Injectable()
export class MenuService {
  constructor(
    @InjectRepository(Menu)
    private menuRepository: Repository<Menu>,
  ) {}

  async findAll(): Promise<MenuDto[]> {
    try {
      const menus = await this.menuRepository.find({
        order: {
          level: 'ASC',
          sortOrder: 'ASC',
        },
      });
      return menus;
    } catch (error) {
      console.error('获取菜单列表失败:', error);
      return [];
    }
  }

  async getMenuTree(): Promise<MenuTreeDto[]> {
    try {
      // 获取所有菜单
      const allMenus = await this.findAll();
      
      // 构建菜单树
      const menuTree = this.buildMenuTree(allMenus);
      
      return menuTree;
    } catch (error) {
      console.error('获取菜单树失败:', error);
      return [];
    }
  }

  private buildMenuTree(menus: MenuDto[], parentId: number | null = null): MenuTreeDto[] {
    const result: MenuTreeDto[] = [];
    
    // 找出当前层级的所有菜单
    const currentLevelMenus = menus.filter(menu => menu.parentId === parentId);
    
    // 为每个菜单添加子菜单
    currentLevelMenus.forEach(menu => {
      const menuWithChildren: MenuTreeDto = {
        ...menu,
        children: this.buildMenuTree(menus, menu.id)
      };
      
      result.push(menuWithChildren);
    });
    
    return result;
  }
}
